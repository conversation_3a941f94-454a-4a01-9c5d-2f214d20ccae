<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirmation Modal Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        body {
            background-color: #f0f1f7;
            padding: 2rem;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .modal-test-card {
            background: white;
            border-radius: 0.5rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-5">Confirmation Modal Component Test</h1>
        
        <!-- Success Modal Test -->
        <div class="modal-test-card">
            <h3 class="text-success mb-3">Success Modal (Green Theme)</h3>
            <p class="text-muted mb-3">Test the success modal type with green color scheme</p>
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#successModal">
                <i class="ri-check-line me-2"></i>Test Success Modal
            </button>
        </div>

        <!-- Info Modal Test -->
        <div class="modal-test-card">
            <h3 class="text-info mb-3">Info Modal (Blue Theme)</h3>
            <p class="text-muted mb-3">Test the info modal type with blue color scheme</p>
            <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#infoModal">
                <i class="ri-information-line me-2"></i>Test Info Modal
            </button>
        </div>

        <!-- Warning Modal Test -->
        <div class="modal-test-card">
            <h3 class="text-warning mb-3">Warning Modal (Yellow Theme)</h3>
            <p class="text-muted mb-3">Test the warning modal type with yellow color scheme</p>
            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#warningModal">
                <i class="ri-alert-line me-2"></i>Test Warning Modal
            </button>
        </div>

        <!-- Danger Modal Test -->
        <div class="modal-test-card">
            <h3 class="text-danger mb-3">Danger Modal (Red Theme) - Default</h3>
            <p class="text-muted mb-3">Test the danger modal type with red color scheme</p>
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#dangerModal">
                <i class="ri-error-warning-line me-2"></i>Test Danger Modal
            </button>
        </div>
    </div>

    <!-- Success Modal -->
    <x-confirmation-modal 
        modal-id="successModal"
        type="success"
        title="Are you sure you want to approve this request?"
        warning-text="This will activate the user account and send a welcome email."
        cancel-text="No, Cancel"
        confirm-text="Yes, Approve"
        form-action="#"
        form-method="PATCH"
    />

    <!-- Info Modal -->
    <x-confirmation-modal 
        modal-id="infoModal"
        type="info"
        title="Send notification to all users?"
        warning-text="This will send an email notification to all registered users."
        cancel-text="No, Cancel"
        confirm-text="Yes, Send"
        form-action="#"
        form-method="POST"
    />

    <!-- Warning Modal -->
    <x-confirmation-modal 
        modal-id="warningModal"
        type="warning"
        title="Are you sure you want to archive this item?"
        warning-text="This item will be moved to the archive and hidden from active listings."
        cancel-text="Keep Active"
        confirm-text="Yes, Archive"
        form-action="#"
        form-method="PATCH"
    />

    <!-- Danger Modal -->
    <x-confirmation-modal 
        modal-id="dangerModal"
        title="Are you sure you want to delete this user?"
        warning-text="This action cannot be undone! All user data will be permanently removed."
        cancel-text="No, Cancel"
        confirm-text="Yes, Delete User"
        form-action="#"
        form-method="DELETE"
    />

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Prevent actual form submission for testing
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    alert('Form submission prevented for testing purposes');
                });
            });
        });
    </script>
</body>
</html>
